apiVersion: apps/v1
kind: Deployment
metadata:
  name: odoo
  labels:
    app: odoo
spec:
  replicas: 1
  selector:
    matchLabels:
      app: odoo
  template:
    metadata:
      labels:
        app: odoo
    spec:
      containers:
      - name: odoo
        image: incubase/odoo:18.0
        ports:
        - containerPort: 8069
        env:
        - name: HOST
          value: "your-db-host"
        - name: PORT
          value: "5432"
        - name: USER
          valueFrom:
            secretKeyRef:
              name: odoo-db-credentials
              key: username
        - name: PASSWORD
          valueFrom:
            secretKeyRef:
              name: odoo-db-credentials
              key: password
        - name: DATABASE
          value: "postgres"
        volumeMounts:
        - name: odoo-data
          mountPath: /app/data
        - name: odoo-custom-addons
          mountPath: /app/custom-addons
      volumes:
      - name: odoo-data
        persistentVolumeClaim:
          claimName: odoo-data-pvc
      - name: odoo-custom-addons
        persistentVolumeClaim:
          claimName: odoo-custom-addons-pvc
