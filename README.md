# Self-Hosted Odoo with Docker

This repository contains Docker configuration files to easily set up and run Odoo.

## Requirements

- Docker
- Docker Compose
- Custom Odoo image: incubase/odoo:18.0

## Quick Start

1. Clone this repository:
   ```
   git clone https://github.com/yourusername/self-host-odoo.git
   cd self-host-odoo
   ```

2. Copy the sample environment file and edit it with your database details:
   ```
   cp sample.env .env
   # Edit .env with your database connection details
   ```

3. Start the container:
   ```
   docker-compose up -d
   ```

4. Access Odoo in your browser:
   ```
   http://localhost:8069
   ```

## Configuration

The setup includes:

- Custom Odoo image (incubase/odoo:18.0)
- Connection to an external PostgreSQL database

### External Database Configuration

This setup is configured to connect to an external PostgreSQL database. Configure your database connection by editing the `.env` file:

```
# Database connection settings
DB_HOST=your_external_db_host
DB_PORT=5432
DB_USER=odoo
DB_PASSWORD=your_secure_password
DB_NAME=postgres
```

Make sure your PostgreSQL database:
1. Is accessible from the Docker container
2. Has the specified user created with appropriate permissions
3. Has the specified database created

## Volumes

Two volumes are configured:
- `odoo-web-data`: Stores Odoo filestore data in `/app/data`
- `./custom-addons`: Directory for your custom Odoo modules

## Custom Modules

Place your custom Odoo modules in the `custom-addons` directory. Each module should be in its own subdirectory with the appropriate Odoo module structure.

## Customization

You can customize the Odoo configuration by modifying environment variables in the `.env` file or by adding additional volumes to the docker-compose.yml file.

## Troubleshooting

If you encounter any issues:

1. Check the logs:
   ```
   docker-compose logs
   ```

2. Restart the services:
   ```
   docker-compose restart
   ```

3. Rebuild the containers:
   ```
   docker-compose down
   docker-compose up -d
   ```
