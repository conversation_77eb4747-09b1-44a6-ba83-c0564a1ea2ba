# Deploying Odoo on Kubernetes (EKS)

This directory contains Kubernetes manifests for deploying Odoo on Kubernetes, including Amazon EKS.

## Prerequisites

- A Kubernetes cluster (e.g., Amazon EKS)
- `kubectl` configured to communicate with your cluster
- An external PostgreSQL database accessible from your Kubernetes cluster

## Deployment Steps

1. Update the database connection information in `deployment.yaml`:
   ```yaml
   env:
   - name: HOST
     value: "your-db-host"  # Update this with your actual database host
   - name: PORT
     value: "5432"          # Update if your database uses a different port
   ```

2. Create a proper secret for database credentials:
   ```bash
   kubectl create secret generic odoo-db-credentials \
     --from-literal=username=odoo \
     --from-literal=password=your-secure-password
   ```
   
   Alternatively, apply the provided secret.yaml after updating the base64-encoded credentials:
   ```bash
   kubectl apply -f secret.yaml
   ```

3. Apply the Kubernetes manifests:
   ```bash
   kubectl apply -f pvc.yaml
   kubectl apply -f deployment.yaml
   kubectl apply -f service.yaml
   ```

4. Check the status of your deployment:
   ```bash
   kubectl get pods
   kubectl get svc
   ```

5. Access Odoo:
   - If using LoadBalancer service type, get the external IP:
     ```bash
     kubectl get svc odoo
     ```
   - Access Odoo at http://<EXTERNAL-IP>

## Configuration

The deployment uses the following environment variables:

- `HOST`: PostgreSQL host address
- `PORT`: PostgreSQL port (default: 5432)
- `USER`: PostgreSQL username
- `PASSWORD`: PostgreSQL password
- `DATABASE`: PostgreSQL database name

## Volumes

Two persistent volumes are used:

- `odoo-data-pvc`: For Odoo data files and attachments
- `odoo-custom-addons-pvc`: For custom Odoo modules

## Troubleshooting

If you encounter issues:

1. Check the pod logs:
   ```bash
   kubectl logs -f deployment/odoo
   ```

2. Check the pod description for events:
   ```bash
   kubectl describe pod -l app=odoo
   ```

3. If environment variables aren't being properly set, check the entrypoint script in the container:
   ```bash
   kubectl exec -it $(kubectl get pod -l app=odoo -o jsonpath="{.items[0].metadata.name}") -- cat /app/entrypoint.sh
   ```
