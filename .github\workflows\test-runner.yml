name: Test Self-Hosted Runner

on:
  # Manual trigger
  workflow_dispatch:
  # You can uncomment the following to also trigger on push
  # push:
  #   branches: [ main ]

jobs:
  test-runner:
    name: Test Self-Hosted Runner
    runs-on: self-hosted
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Display success message
        run: |
          echo "🎉 Success! The self-hosted runner is working correctly!"
          echo "Runner name: ${{ runner.name }}"
          echo "Runner OS: ${{ runner.os }}"
          echo "Runner architecture: ${{ runner.arch }}"
      
      - name: Display environment information
        run: |
          echo "🔍 Environment Information:"
          echo "------------------------------"
          echo "GitHub workspace: ${{ github.workspace }}"
          echo "GitHub event name: ${{ github.event_name }}"
          echo "GitHub repository: ${{ github.repository }}"
          
      - name: Test system commands
        run: |
          echo "💻 System Information:"
          echo "------------------------------"
          echo "Current directory:"
          pwd
          echo "Directory listing:"
          ls -la
          echo "Available disk space:"
          df -h
