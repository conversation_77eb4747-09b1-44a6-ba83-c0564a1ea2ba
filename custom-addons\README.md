# Custom Addons for Odoo

This directory is mounted into the Odoo container and can be used to add custom modules.

## How to use

1. Place your custom Odoo modules in this directory
2. Each module should be in its own subdirectory
3. The modules will be automatically detected by Odoo

## Example structure

```
custom-addons/
├── my_module1/
│   ├── __init__.py
│   ├── __manifest__.py
│   ├── models/
│   ├── views/
│   └── ...
├── my_module2/
│   ├── __init__.py
│   ├── __manifest__.py
│   └── ...
└── README.md
```
