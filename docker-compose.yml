version: '3'
services:
  # Odoo service using locally built image
  web:
    image: incubasestudio.com/odoo:18.0
    build:
      context: .
      args:
        - DB_HOST=${DB_HOST}
        - DB_PORT=${DB_PORT}
        - DB_USER=${DB_USER}
        - DB_PASSWORD=${DB_PASSWORD}
        - DB_NAME=${DB_NAME}
        - DATA_DIR=/app/data
        - ADDONS_PATH=/app/odoo/addons,/app/addons,/app/custom-addons
        # - ADDONS_PATH=/app/addons,/app/custom-addons
    ports:
      - "8069:8069"
      - "8071:8071"
      - "8072:8072"
    volumes:
      - odoo-web-data:/app/data
      - custom-addons:/app/custom-addons
      # - /volume1/docker/odoo/web-data:/app/data:rw
      # - /volume1/docker/odoo/custom-addons:/app/custom-addons:rw
    environment:
      - HOST=${DB_HOST}
      - PORT=${DB_PORT}
      - USER=${DB_USER}
      - PASSWORD=${DB_PASSWORD}
      - DATABASE=${DB_NAME}
    restart: always

volumes:
  odoo-web-data:
  custom-addons:
