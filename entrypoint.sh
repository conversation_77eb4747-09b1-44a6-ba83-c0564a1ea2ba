#!/bin/sh
set -e

# 設定環境變數的預設值（如果未提供）
HOST=${HOST:-localhost}
PORT=${PORT:-5432}
USER=${USER:-odoo}
PASSWORD=${PASSWORD:-odoo}
DATABASE=${DATABASE:-postgres}
DATA_DIR=${DATA_DIR:-/app/data}
ADDONS_PATH=${ADDONS_PATH:-/app/odoo/addons,/app/addons,/app/custom-addons}
# ADDONS_PATH=${ADDONS_PATH:-/app/addons,/app/custom-addons}

# 輸出啟動時的配置信息
echo "Starting Odoo with the following configuration:"
echo "Database host: $HOST"
echo "Database port: $PORT"
echo "Database user: $USER"
echo "Database name: $DATABASE"
echo "Data directory: $DATA_DIR"
echo "Addons path: $ADDONS_PATH"

# 確保數據目錄存在並設置正確權限
mkdir -p "$DATA_DIR"
mkdir -p "/app/addons"
mkdir -p "/app/custom-addons"
# chown -R 1000:1000 /app/custom-addons
# chmod -R 755 /app/custom-addons

# 等待 PostgreSQL 可用
echo "Waiting for PostgreSQL to be available..."
until PGPASSWORD="$PASSWORD" psql -h "$HOST" -U "$USER" -p "$PORT" -d "$DATABASE" -c '\q' 2>/dev/null; do
  sleep 2
  echo "PostgreSQL is not ready yet, retrying..."
done
echo "PostgreSQL is up!"

DUMMY_ADDON_PATH="/app/custom-addons/dummy_module"

if [ ! -d "$DUMMY_ADDON_PATH" ]; then
    mkdir -p "$DUMMY_ADDON_PATH"
    echo -e "# -*- coding: utf-8 -*-\nfrom . import models" > "$DUMMY_ADDON_PATH/__init__.py"
    echo -e "{\n  \"name\": \"Dummy Module\",\n  \"version\": \"1.0\",\n  \"depends\": [],\n  \"installable\": True,\n  \"application\": False\n}" > "$DUMMY_ADDON_PATH/__manifest__.py"
    echo "Created dummy module in $DUMMY_ADDON_PATH"
fi

exec python /app/odoo-bin \
    --db_host="$HOST" \
    --db_port="$PORT" \
    --db_user="$USER" \
    --db_password="$PASSWORD" \
    --database="$DATABASE" \
    --data-dir="$DATA_DIR" \
    --addons-path="$ADDONS_PATH" \
    --limit-time-cpu=600 \
    --limit-time-real=1200 \
    --without-demo=all \
    "$@"
