FROM python:3.10.12-alpine3.18

# Set environment variables
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apk add --no-cache \
    git \
    build-base \
    libxml2-dev \
    libxslt-dev \
    openldap-dev \
    libjpeg-turbo-dev \
    zlib-dev \
    postgresql-dev \
    freetype-dev \
    lcms2-dev \
    libwebp-dev \
    harfbuzz-dev \
    fribidi-dev \
    postgresql-client \
    npm \
    nodejs \
    wkhtmltopdf \
    python3-dev \
    py3-cython

# Create app directory
RUN mkdir -p /app
WORKDIR /app

# Clone a specific version of Odoo that is known to work with Python 3.10
RUN git clone --depth=1 --branch=16.0 https://github.com/odoo/odoo.git .

# Install Python dependencies in the correct order
RUN pip install --no-cache-dir --upgrade pip setuptools wheel
RUN pip install --no-cache-dir Werkzeug==2.2.3
RUN pip install --no-cache-dir psycopg2-binary==2.9.9
RUN pip install --no-cache-dir greenlet==2.0.2
RUN pip install --no-cache-dir gevent==22.10.2

# Install remaining dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Set up Odoo configuration
RUN mkdir -p /app/data/filestore

# Expose Odoo port
EXPOSE 8069 8071 8072

# Set the default command to run Odoo
CMD ["python", "/app/odoo-bin", \
     "--db_host=${HOST}", \
     "--db_port=${PORT}", \
     "--db_user=${USER}", \
     "--db_password=${PASSWORD}", \
     "--database=${DATABASE}", \
     "--data-dir=/app/data", \
     "--addons-path=/app/addons"]
