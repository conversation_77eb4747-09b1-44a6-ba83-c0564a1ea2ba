FROM odoo:16.0

# Create app directory structure
RUN mkdir -p /app/data/filestore

# Copy custom configuration
COPY ./odoo.conf /etc/odoo/

USER root

# Install additional dependencies if needed
RUN apt-get update && apt-get install -y --no-install-recommends \
    git \
    python3-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Switch back to odoo user
USER odoo

# Set the default command to run Odoo
CMD ["odoo", "--config=/etc/odoo/odoo.conf"]
