name: Auto Versioning & Release

on:
  push:
    branches:
      - main
      - releases/uat
      - releases/prd
      
env:
  ECR_DOMAIN: ${{secrets.VEXMETA_ECR_AWS_ACCOUNT_ID}}.dkr.ecr.${{secrets.VEXMETA_ECR_AWS_REGION}}.amazonaws.com
  ECR_REPOSITORY: incubase-odoo
  IMAGE_TAG: ${{ github.sha }}
  K8S_CLUSTER_NAME: vex-runtime-server
  K8S_BASIC_NAMESPACE_NAME: incubase
  K8S_WORKLOAD_NAME: incubase-odoo
    
jobs:
  
  bump-version:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Configure Git
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

      - name: Get current version
        id: get-version
        run: |
          VERSION=$(cat VERSION 2>/dev/null || echo "1.0.0")
          echo "Current version: $VERSION"
          echo "VERSION=${VERSION}" >> $GITHUB_ENV

      - name: Bump version
        id: bump-version
        run: |
          # Parse version components
          CURRENT_VERSION=$(cat VERSION)
          MAJOR=$(echo $CURRENT_VERSION | cut -d. -f1)
          MINOR=$(echo $CURRENT_VERSION | cut -d. -f2)
          PATCH=$(echo $CURRENT_VERSION | cut -d. -f3)

          # Get the last commit message
          COMMIT_MSG=$(git log -1 --pretty=%B)
          echo "Commit message: $COMMIT_MSG"

          # Determine which version component to bump based on commit message
          if echo "$COMMIT_MSG" | grep -i -E "add|new|migrate" > /dev/null; then
            # Bump major version
            NEW_MAJOR=$((MAJOR + 1))
            NEW_MINOR=0
            NEW_PATCH=0
            VERSION_TYPE="major"
          elif echo "$COMMIT_MSG" | grep -i -E "update" > /dev/null; then
            # Bump minor version
            NEW_MAJOR=$MAJOR
            NEW_MINOR=$((MINOR + 1))
            NEW_PATCH=0
            VERSION_TYPE="minor"
          else
            # Default: Bump patch version (for 'fix', 'debug', 'patch', or any other message)
            NEW_MAJOR=$MAJOR
            NEW_MINOR=$MINOR
            NEW_PATCH=$((PATCH + 1))
            VERSION_TYPE="patch"
          fi

          NEW_VERSION="$NEW_MAJOR.$NEW_MINOR.$NEW_PATCH"
          echo "Bumping $VERSION_TYPE version to: $NEW_VERSION"

          echo "New version: $NEW_VERSION"
          echo "$NEW_VERSION" > VERSION
          echo "NEW_VERSION=$NEW_VERSION" >> $GITHUB_ENV

      - name: Commit & Push updated version
        run: |
          git add VERSION
          git commit -m "Bump version to $NEW_VERSION [skip ci]"
          git push origin HEAD:${GITHUB_REF#refs/heads/}

  push-and-release:
    runs-on: self-hosted
    strategy:
      matrix:
        environment:
          - ${{ contains(github.ref, 'releases/uat') && 'uat' || contains(github.ref, 'releases/prd') && 'prd' }}
    if: contains(github.ref, 'releases')
    outputs:
      ecr_full_path: ${{ steps.build-push.outputs.ecr_full_path }}
      image_version: ${{ steps.extract-version.outputs.version }}
      registry_uri: ${{ steps.login-ecr.outputs.registry }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Configure Git Identity
        run: |
          git --version
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

      - name: Get current version
        id: extract-version
        run: |
          VERSION=$(cat VERSION)
          echo "VERSION=$VERSION"
    
          # Set output and env variables
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          echo "version=$VERSION" >> $GITHUB_OUTPUT

      - name: Define Git Tag Prefix
        run: |
          TAG_PREFIX="${{ matrix.environment }}-v"
          echo "TAG_PREFIX=$TAG_PREFIX" >> $GITHUB_ENV

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.VEXMETA_ECR_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.VEXMETA_ECR_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.VEXMETA_ECR_AWS_REGION }}
      
      - name: Install AWS CLI
        run: |
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          unzip awscliv2.zip
          ./aws/install -i /home/<USER>/.local/aws-cli -b /home/<USER>/.local/bin --update
          rm -rf awscliv2.zip aws
          echo "/home/<USER>/.local/bin" >> $GITHUB_PATH

      - name: Define ECR Repository Name
        run: |
          ECR_REPOSITORY="${{ env.ECR_REPOSITORY }}-${{ matrix.environment }}"
          echo "ECR_REPOSITORY=$ECR_REPOSITORY" >> $GITHUB_ENV

      - name: Login to Amazon ECR
        id: login-ecr
        run: |
          aws ecr get-login-password --region ${{ secrets.VEXMETA_ECR_AWS_REGION }} | docker login --username AWS --password-stdin ${{ env.ECR_DOMAIN }}
          REGISTRY="$(aws ecr describe-repositories --repository-names $ECR_REPOSITORY --query 'repositories[0].repositoryUri' --output text || aws ecr create-repository --repository-name $ECR_REPOSITORY --query 'repository.repositoryUri' --output text)"
          echo "REGISTRY=$REGISTRY"

          echo "REGISTRY=$REGISTRY" >> $GITHUB_ENV
          echo "registry=$REGISTRY" >> $GITHUB_OUTPUT

      - name: Build and push image with docker
        id: build-push
        run: |
          # Define the full image path
          FULL_IMAGE_PATH="${{ steps.login-ecr.outputs.registry }}:v${{ steps.extract-version.outputs.version }}"
          echo "Full image path: $FULL_IMAGE_PATH"
          echo "FULL_IMAGE_PATH=$FULL_IMAGE_PATH"

          # Build and push the image
          docker build -t $FULL_IMAGE_PATH .
          docker push $FULL_IMAGE_PATH
          
          # Set ECR full path for next job
          echo "FULL_IMAGE_PATH=$FULL_IMAGE_PATH" >> $GITHUB_ENV
          echo "ecr_full_path=$FULL_IMAGE_PATH" >> $GITHUB_OUTPUT
          
          # Debug output
          echo "Setting output ecr_full_path to: $FULL_IMAGE_PATH"

      - name: Publish Release on GitHub
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ env.TAG_PREFIX }}${{ env.VERSION }}
          name: "Release ${{ env.TAG_PREFIX }}${{ env.VERSION }}"
          files: "odoo-${{ matrix.environment }}-v${{ env.VERSION }}.tar.gz"
          body: "Odoo version ${{ env.TAG_PREFIX }}${{ env.VERSION }} released!"

      - name: Save registry info
        run: |
          echo "${{ steps.login-ecr.outputs.registry }}" > registry_uri.txt
          echo "${{ steps.build-push.outputs.ecr_full_path }}" > ecr_full_path.txt
          echo "${{ steps.extract-version.outputs.version }}" > image_version.txt

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: env-info
          path: |
            registry_uri.txt
            ecr_full_path.txt
            image_version.txt

  deploy-to-eks:
    needs: push-and-release
    runs-on: ubuntu-latest
    strategy:
      matrix:
        environment:
        - ${{ contains(github.ref, 'releases/uat') && 'uat' || contains(github.ref, 'releases/prd') && 'prd' }}
    if: contains(github.ref, 'releases')

    steps:
      - name: Checkout code for fallback method
        uses: actions/checkout@v3

      - name: Download env info
        uses: actions/download-artifact@v4
        with:
          name: env-info

      - name: Load env info
        run: |
          REGISTRY_URI=$(cat registry_uri.txt)
          echo "REGISTRY_URI=$REGISTRY_URI" >> $GITHUB_ENV
          ECR_FULL_PATH=$(cat ecr_full_path.txt)
          echo "ECR_FULL_PATH=$ECR_FULL_PATH" >> $GITHUB_ENV
          IMAGE_VERSION=$(cat image_version.txt)
          echo "IMAGE_VERSION=$IMAGE_VERSION" >> $GITHUB_ENV

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.VEXMETA_K8S_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.VEXMETA_K8S_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.VEXMETA_K8S_AWS_REGION }}
      
      - name: Install eksctl
        run: |
          # Download and install eksctl
          curl --silent --location "https://github.com/weaveworks/eksctl/releases/latest/download/eksctl_$(uname -s)_amd64.tar.gz" | tar xz -C /tmp
          sudo mv /tmp/eksctl /usr/local/bin
          # Verify installation
          eksctl version

      - name: Connect to EKS cluster
        run: |
          aws eks update-kubeconfig --name ${K8S_CLUSTER_NAME} --region ${{ secrets.VEXMETA_K8S_AWS_REGION }}
          eksctl utils write-kubeconfig --cluster=${K8S_CLUSTER_NAME} --region=${{ secrets.VEXMETA_K8S_AWS_REGION }}
      
      - name: Install kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'

      - name: Define Namespace for Environment
        run: |
          case "${{ matrix.environment }}" in
            "dev"|"uat")
              K8S_NAMESPACE_NAME="${K8S_BASIC_NAMESPACE_NAME}-private"
              ;;
            "preprd"|"prd")
              K8S_NAMESPACE_NAME="${K8S_BASIC_NAMESPACE_NAME}-public"
              ;;
            *)
              echo "ERROR: Unknown environment '${{ matrix.environment }}'"
              exit 1
              ;;
          esac
          echo "K8S_NAMESPACE_NAME=$K8S_NAMESPACE_NAME" >> $GITHUB_ENV

      - name: Deploy to EKS
        run: |
          # Debug output
          echo "Retrieved from previous job:"
          echo "  - REGISTRY_URI: $REGISTRY_URI"
          echo "  - IMAGE_VERSION: $IMAGE_VERSION"
          echo "  - ECR_FULL_PATH: $ECR_FULL_PATH"

          # Construct the path using multiple methods to ensure we have a valid path
          if [ -z "$ECR_FULL_PATH" ]; then
            echo "Warning: ECR_FULL_PATH is empty, trying alternative methods"
            
            # Method 1: Use registry_uri and image_version outputs
            if [ -n "$REGISTRY_URI" ] && [ -n "$IMAGE_VERSION" ]; then
              ECR_FULL_PATH="${REGISTRY_URI}:v${IMAGE_VERSION}"
              echo "Method 1: Constructed ECR_FULL_PATH: $ECR_FULL_PATH"
            # Method 2: Use VERSION file
            elif [ -f "VERSION" ]; then
              VERSION=$(cat VERSION | tr -d '[:space:]')
              ECR_FULL_PATH="${{env.ECR_DOMAIN}}/${{env.ECR_REPOSITORY}}:v$VERSION"
              echo "Method 2: Using VERSION file: $ECR_FULL_PATH"
            # No fallback - fail the job if we can't determine the image path
            else
              echo "ERROR: Could not determine ECR image path using any available method"
              echo "Both Method 1 (outputs from previous job) and Method 2 (VERSION file) failed"
              exit 1
            fi
          fi

          echo "Deploy Deployment [${K8S_WORKLOAD_NAME}-${{ matrix.environment }}] with Image [$ECR_FULL_PATH] in [${K8S_NAMESPACE_NAME}]"
          kubectl set image deployment/${K8S_WORKLOAD_NAME}-${{ matrix.environment }} ${K8S_WORKLOAD_NAME}-${{ matrix.environment }}=${ECR_FULL_PATH} -n ${K8S_NAMESPACE_NAME}
          
          # Verify deployment
          kubectl rollout status deployment/${K8S_WORKLOAD_NAME}-${{ matrix.environment }} -n ${K8S_NAMESPACE_NAME} --timeout=120s
